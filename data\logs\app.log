2025-07-30 23:15:45,015 INFO: 系统日志已被管理员清除 [in /app/api/log_api.py:112]
2025-07-30 23:18:33,178 INFO: 收到重启容器请求 [in /app/api/settings_api.py:119]
2025-07-30 23:18:33,178 INFO: 尝试向supervisor发送重启信号 [in /app/api/settings_api.py:123]
2025-07-30 23:18:33,178 INFO: === 服务重启成功 === [in /app/api/settings_api.py:132]
2025-07-30 23:18:33,178 INFO: 已向supervisor进程(1)发送SIGHUP信号 [in /app/api/settings_api.py:133]
2025-07-30 23:18:38,088 INFO: === Jassistant v1.0.6.1 Web服务启动成功 === [in /app/app.py:122]
2025-07-30 23:18:38,088 INFO: Web服务已在端口34711启动，PID: 19 [in /app/app.py:123]
2025-07-30 23:18:38,090 INFO: 数据库初始化完成 [in /app/app.py:129]
2025-07-30 23:18:38,091 INFO: 性能监控系统已启动 [in /app/app.py:136]
2025-07-30 23:18:38,091 INFO: Web服务初始化完成，调度器由独立进程管理 [in /app/app.py:142]
2025-07-31 09:00:00,004 INFO: 开始执行每日报告任务... [in /app/notification_sender.py:316]
2025-07-31 09:00:00,015 INFO: 查询昨天(2025-07-30)的入库记录 [in /app/notification_sender.py:345]
2025-07-31 09:00:00,015 INFO: 时间范围: 2025-07-30 00:00:00 到 2025-07-31 00:00:00 [in /app/notification_sender.py:346]
2025-07-31 09:00:00,015 INFO: 昨天(2025-07-30)入库的影片数量: 0 [in /app/notification_sender.py:359]
2025-07-31 09:00:00,015 INFO: 昨天无新入库影片，不发送报告。 [in /app/notification_sender.py:383]
